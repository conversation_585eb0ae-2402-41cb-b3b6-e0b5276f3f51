<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>CYVIRNET | Our Experience</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

   <!-- Favicon -->
   <link rel="icon" type="image/x-icon" href="https://images.squarespace-cdn.com/content/v1/659c01d1b9d869462047df7b/6b205281-0425-4502-8b43-b4622a33598a/favicon.ico?format=100w">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Inter:wght@700;800&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">
    <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Bootstrap JS -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/style C.css" rel="stylesheet">

    <style>.about-section {padding: 90px 0px;}.service-section{padding: 90px 0px;}.team-sections{padding: 70px 0px;}.why-sections{padding-top: 70px;
    }.testimonials-sections{padding-top: 70px;}.our-portfolio-sections{padding-top: 70px;}.col-lg-7 {text-align: center;}                   #footer .nav-link {
                       color: #d2c9c9; /* Change link color to white */
                       font-family:'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
                   }</style>
</head>

    <body class="bg-white">
        <div class="bg-white p-0">
           <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <img src="img/CYVIRNET Logo-01.png" style="width: 100px; height: auto;" alt="Loading...">
        </div>
        </div>
        <!-- Spinner End -->


    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow fixed-top w-100">        <a href="index.html" class="navbar-brand d-flex align-items-center text-center px-4 px-lg-5">
            <img src="img/Log.jpg" class="img-fluid" alt="Logo" style="width: 48%; height: auto;">
        </a>
        <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="index.html" class="nav-item nav-link active">Home</a>
                <div class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">About</a>
                    <div class="dropdown-menu fade-down m-0">
                        <a href="about.html" class="dropdown-item">About Us</a>
                        <a href="Our_Portifolio.html" class="dropdown-item">Our Portfolio</a>
                        <a href="testimonials.html" class="dropdown-item">Testimonial</a>
                    </div>
                </div>
                <a href="service.html" class="nav-item nav-link">Services</a>
                <a href="team.html" class="nav-item nav-link">Our Team</a>
                <div class="pe-5 py-2">
                    <a href="contact.html">
                        <button class="button rounded px-lg-5 d-md-block">Contact Us</button>
                    </a>
                </div>
            </div>
        </div>
    </nav>    <!-- Navbar End -->
    
    <!-- Enhanced Hero Section for Team Page -->
    <section class="team-hero-section">
        <div class="hero-background"></div>
        <div class="hero-overlay"></div>
        
        <!-- Animated particles -->
        <div class="hero-particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>
        
        <div class="container">
            <div class="row align-items-center min-vh-60">
                <div class="col-lg-8 col-md-10 mx-auto text-center">
                    <div class="hero-content">
                        <div class="hero-badge">
                            <i class="fas fa-users me-2"></i>
                            <span>Professional Excellence</span>
                        </div>
                        <h1 class="team-hero-title">
                            Meet Our Expert Team
                        </h1>                        <p class="team-hero-description">
                            Our team has over a decade's worth of experience in IT, networking, and cybersecurity, 
                            making us a steadfast pillar of expertise in the industry.
                        </p>
                    </div>
                </div>
            </div>        </div>
    </section>
    <!-- End Enhanced Hero Section -->

    <style>
    /* === TEAM HERO SECTION === */    .team-hero-section {
        position: relative;
        min-height: 60vh;
        display: flex;
        align-items: center;
        overflow: hidden;
        padding: 120px 0 30px; /* Increased top padding to account for fixed navbar */
        background: linear-gradient(rgba(26,35,126,0.7), rgba(13,71,161,0.6)), url('img/**********.jpg');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-attachment: fixed;
    }

    .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        background-image: 
            radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 1px, transparent 1px),
            radial-gradient(circle at 80% 80%, rgba(255,255,255,0.08) 1px, transparent 1px),
            radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 2px, transparent 2px);
        background-size: 100px 100px, 150px 150px, 200px 200px;
    }    .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.1);
        z-index: 2;
    }

    .hero-particles {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 3;
    }

    .particle {
        position: absolute;
        width: 3px;
        height: 3px;
        background: rgba(255,255,255,0.6);
        border-radius: 50%;
        animation: particleFloat 6s ease-in-out infinite;
    }

    .particle:nth-child(1) {
        top: 20%;
        left: 10%;
        animation-delay: -1s;
        animation-duration: 8s;
    }

    .particle:nth-child(2) {
        top: 60%;
        left: 80%;
        animation-delay: -3s;
        animation-duration: 10s;
    }

    .particle:nth-child(3) {
        top: 40%;
        left: 60%;
        animation-delay: -2s;
        animation-duration: 7s;
    }

    .particle:nth-child(4) {
        top: 80%;
        left: 20%;
        animation-delay: -4s;
        animation-duration: 9s;
    }

    .particle:nth-child(5) {
        top: 30%;
        left: 90%;
        animation-delay: -1.5s;
        animation-duration: 6s;
    }    .hero-content {
        position: relative;
        z-index: 10;
        animation: heroContentSlideUp 1.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        margin-top: 80px; /* Add top margin to push content below navbar */
    }

    .hero-badge {
        display: inline-flex;
        align-items: center;
        background: rgba(255,255,255,0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        border-radius: 50px;
        padding: 8px 18px;
        font-size: 0.9rem;
        color: #ffffff;
        font-weight: 500;
        margin-bottom: 1.8rem;
        animation: heroContentSlideUp 1.4s cubic-bezier(0.4, 0, 0.2, 1) both;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }    .team-hero-title {
        font-size: clamp(2.5rem, 6vw, 4rem);
        font-weight: 800;
        line-height: 1.1;
        color: #ffffff;
        margin-bottom: 1.4rem;
        text-shadow: 0 2px 20px rgba(0,0,0,0.3);
        text-align: center;
        animation: heroLineSlideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
        animation-delay: 0.2s;
    }

    .team-hero-description {
        font-size: clamp(1.1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        color: #ffffff;
        margin-bottom: 2.5rem;
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
        text-align: center;
        animation: heroContentSlideUp 1.6s cubic-bezier(0.4, 0, 0.2, 1) both;
    }

    .team-stats {
        display: flex;
        gap: 2.5rem;
        justify-content: center;
        flex-wrap: wrap;
        animation: heroContentSlideUp 2s cubic-bezier(0.4, 0, 0.2, 1) both;
    }

    .stat-item {
        text-align: center;
        background: rgba(255,255,255,0.05);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.1);
        border-radius: 15px;
        padding: 20px 25px;
        transition: all 0.3s ease;
        min-width: 120px;
    }

    .stat-item:hover {
        background: rgba(255,255,255,0.1);
        border-color: rgba(255,255,255,0.2);
        transform: translateY(-5px);
    }

    .stat-number {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        font-weight: 800;
        color: #ffffff;
        line-height: 1;
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-label {
        font-size: 0.9rem;
        color: rgba(255,255,255,0.8);
        font-weight: 500;
    }

    .hero-scroll-indicator {
        position: absolute;
        bottom: 25px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 10;
        animation: heroScrollIndicator 2s ease-in-out infinite;
    }

    .scroll-arrow {
        width: 38px;
        height: 38px;
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .scroll-arrow:hover {
        background: rgba(255,255,255,0.2);
        border-color: rgba(255,255,255,0.5);
        transform: scale(1.1);
    }    /* === DOUBLED SIZE CERTIFICATION LOGOS === */
    .section .team-wrap .team-img img.responsive-img {
        width: 70% !important;
        height: auto !important;
        max-width: 120px !important;
        transition: transform 0.3s ease;
    }
    
    .section .team-wrap .team-img img.responsive-img:hover {
        transform: scale(1.1);
    }
    
    .section .team-wrap .team-member {
        padding: 1rem;
    }
    
    .section .team-wrap .team-img {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 80px;
    }
    
    /* Remove inline styles effect */
    .section .team-wrap img[style*="width"] {
        width: 70% !important;
        max-width: 120px !important;
    }
    
    /* === CERTIFICATION LOGOS SIZE REDUCTION === */
    @keyframes particleFloat {
        0%, 100% { transform: translateY(0) translateX(0); opacity: 0.6; }
        25% { transform: translateY(-15px) translateX(8px); opacity: 1; }
        50% { transform: translateY(-8px) translateX(-4px); opacity: 0.8; }
        75% { transform: translateY(-12px) translateX(12px); opacity: 0.9; }
    }

    @keyframes heroContentSlideUp {
        0% { opacity: 0; transform: translateY(50px); }
        100% { opacity: 1; transform: translateY(0); }
    }

    @keyframes heroLineSlideUp {
        0% { opacity: 0; transform: translateY(40px); }
        100% { opacity: 1; transform: translateY(0); }
    }

    @keyframes heroScrollIndicator {
        0%, 100% { transform: translateX(-50%) translateY(0); }
        50% { transform: translateX(-50%) translateY(8px); }
    }    /* === RESPONSIVE === */
    @media (max-width: 991.98px) {
        .team-hero-section {
            min-height: 55vh;
            padding: 100px 0 20px; /* Maintain navbar offset */
        }
        .hero-content {
            margin-top: 60px; /* Adjusted margin for medium screens */
        }
        .team-stats {
            gap: 1.8rem;
        }
    }

    @media (max-width: 767.98px) {
        .team-hero-section {
            min-height: 50vh;
            padding: 90px 0 15px; /* Maintain navbar offset for tablets */
        }
        .hero-content {
            margin-top: 50px; /* Adjusted margin for tablets */
        }
        .team-stats {
            gap: 1.2rem;
        }
        .stat-item {
            min-width: 100px;
            padding: 15px 20px;
        }
    }    @media (max-width: 575.98px) {
        .team-hero-section {
            min-height: 45vh;
            padding: 110px 0 10px; /* Increased mobile padding to prevent cutoff */
        }
        .hero-content {
            margin-top: 40px; /* Adjusted margin for mobile */
        }
        .hero-badge {
            font-size: 0.8rem;
            padding: 6px 14px;
        }
        .team-stats {
            gap: 1rem;
        }        .stat-item {
            padding: 12px 16px;
        }
    }

    /* Extra small mobile screens */
    @media (max-width: 480px) {
        .team-hero-section {
            padding: 120px 0 10px; /* Even more padding for very small screens */
        }
        .hero-content {
            margin-top: 30px; /* Reduced margin for very small screens */
        }
    }

    @media (max-width: 360px) {
        .team-hero-section {
            padding: 130px 0 10px; /* Maximum padding for smallest screens */
        }
        .hero-content {
            margin-top: 20px; /* Minimal margin for smallest screens */
        }
    }

    /* === TEAM SECTION EQUAL HEIGHT FIX === */
    .team-content-row {
        display: flex;
        align-items: stretch;
    }

    .team-content-row .col-md-6 {
        display: flex;
        flex-direction: column;
    }

    .team-image-container {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .team-image-container img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 0.375rem;
    }

    @media (max-width: 767.98px) {
        .team-content-row {
            flex-direction: column;
        }

        .team-image-container img {
            min-height: 300px;
        }
    }
    </style>


  <!-- Contact Start -->
  <div class="container-xxl py-5">
    <div class="container">
        <div class="row g-4 team-content-row">            <div class="col-md-6 wow">
              <div class="h-100 d-flex flex-column justify-content-center">
                <h2 class="d-inline-block mb-3">Our Team</h2>

                <!-- Core Expertise Areas -->
                <p class="text-start mb-3">Our core areas of expertise include:</p>
                <ul class="list-unstyled mb-4">
                    <li class="fw-medium text-start pe-2 mb-2"><i class="fa fa-check text-success me-3"></i>IT</li>
                    <li class="fw-medium text-start mb-2"><i class="fa fa-check text-success me-3"></i>Networking</li>
                    <li class="fw-medium text-start mb-2"><i class="fa fa-check text-success me-3"></i>Cybersecurity</li>
                </ul>

                <!-- Main Content -->
                <p class="text-start">Our team has over a decade's worth of experience in IT, networking,
                    and cybersecurity, making us a steadfast pillar of expertise in the industry. Since our
                    founding, we have continually refined our skills and knowledge, staying
                    at the forefront of technological advancements. From implementing
                    innovative networking solutions and robust cybersecurity measures,
                    our seasoned professionals bring a wealth of hands-on experience to
                    every project. Our list of certifications and accreditations includes
                    but is not limited to certifications from:</p>
              </div>
            </div>            <div class="col-md-6">
                <div class="team-image-container">
                  <img src="img/img4.jpg" alt="Our Team">
                </div>
            </div>
        </div>
        
    </div>
</div>
<!-- Contact End -->


<section class="section py-3">      <div class="row">
        <div class="col-lg-12">            <!-- Category Start -->
            <div class="row g-3">
                <div class="col-6 col-md-3 team-wrap">
                    <div class="team-member text-center">
                        <div class="team-img">
                            <img class="responsive-img" src="img/brands/aws.png" style="width: 90%; height: auto;" alt="AWS Certification">
                        </div>
                    </div>
                </div>
                
                <div class="col-6 col-md-3 team-wrap">
                    <div class="team-member text-center">
                        <div class="team-img">
                            <img class="responsive-img" src="img/brands/juniper.png" style="width: 90%; height: auto;" alt="Juniper Certification">
                        </div>
                    </div>
                </div>

                <div class="col-6 col-md-3 team-wrap">
                    <div class="team-member text-center">
                        <div class="team-img">
                            <img class="responsive-img" src="img/brands/ccna.png" style="width: 90%; height: auto;" alt="Cisco CCNA Certification">
                        </div>
                    </div>
                </div>
                
                <div class="col-6 col-md-3 team-wrap">
                    <div class="team-member text-center">
                        <div class="team-img">
                            <img class="responsive-img" src="img/brands/oscp.png" style="width: 90%; height: auto;" alt="OSCP Certification">
                        </div>
                    </div>
                </div>

                <div class="col-6 col-md-3 team-wrap mt-3">
                    <div class="team-member text-center">
                        <div class="team-img">
                            <img class="responsive-img" src="img/brands/ceh.png" style="width: 90%; height: auto;" alt="CEH Certification">
                        </div>
                    </div>
                </div>

                <div class="col-6 col-md-3 team-wrap mt-3">
                    <div class="team-member text-center">
                        <div class="team-img">
                            <img class="responsive-img" src="img/brands/cisa.png" style="width: 90%; height: auto;" alt="CISA Certification">
                        </div>
                    </div>
                </div>

                <div class="col-6 col-md-3 team-wrap mt-3">
                    <div class="team-member text-center">
                        <div class="team-img">
                            <img class="responsive-img" src="img/brands/giac.png" style="width: 90%; height: auto;" alt="GIAC Certification">
                        </div>
                    </div>
                </div>

                <div class="col-6 col-md-3 team-wrap mt-3">
                    <div class="team-member text-center">
                        <div class="team-img">
                            <img class="responsive-img" src="img/brands/comp.png" style="width: 90%; height: auto;" alt="CompTIA Certification">
                        </div>
                    </div>
                </div>
            </div>
            <!-- Category End -->
        </div>
      </div>
    
  </section>

 <!-- Footer Start -->
 <section id="footer">
        <footer class="bg-danger">
            <div class="container">              <div class="row justify-content-center">
                <!-- Quick Links removed as per request -->
                <div class="col-md-6 footer-column text-center">
                  <ul class="nav flex-column">
                    <li class="nav-item">
                      <span class="footer-title">Contact & Support</span>
                    </li>
                    <li class="nav-item d-flex align-items-center justify-content-center">
                      <span class="nav-link me-3" style="display: flex; align-items: center; flex-wrap: wrap;"><i class="fas fa-phone me-2"></i>+263 8677010124</span>
                      <span class="nav-link" style="display: flex; align-items: center; flex-wrap: wrap;"><i class="fas fa-envelope me-2"></i><EMAIL></span>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="row justify-content-center mt-3">
                <div class="col-lg-3 col-md-6 text-center">
                    <img src="img/logg.png" class="img-fluid">
                </div>
              </div>
            </div>
          </footer>
        <!-- Footer End -->
        
        
              <!--Back to Top-->
                <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
            </div>
    </section>
<!-- Footer End -->



        <!-- Back to Top -->
        <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    <script src="lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="js/main.js"></script>
    <!-- jQuery (necessary for Bootstrap's JavaScript plugins) -->
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script>
        $(".panel-collapse").on("show.bs.collapse", function () {
          $(this).siblings(".panel-heading").addClass("active");
        });
      
        $(".panel-collapse").on("hide.bs.collapse", function () {
          $(this).siblings(".panel-heading").removeClass("active");
        });
      </script>
</body>

</html>
