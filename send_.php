<?php
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $name = filter_var(trim($_POST["name"]), FILTER_SANITIZE_STRING);
    $email = filter_var(trim($_POST["email"]), FILTER_SANITIZE_EMAIL);
    $subject = filter_var(trim($_POST["subject"]), FILTER_SANITIZE_STRING);
    $message = trim($_POST["message"]);

    // Validate inputs
    if (empty($name) || empty($email) || empty($subject) || empty($message) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        header("Location: contact.html?status=error&message=Please fill all fields correctly.");
        exit;
    }

    $recipient = "<EMAIL>"; // The recipient email address
    $email_subject = "New Contact Form Submission: $subject";
    $email_body = "Name: $name\n";
    $email_body .= "Email: $email\n\n";
    $email_body .= "Message:\n$message";

    $headers = "From: $name <$email>\r\n";
    $headers .= "Reply-To: $email\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

    if (mail($recipient, $email_subject, $email_body, $headers)) {
        header("Location: contact.html?status=success&message=Your message has been sent successfully!");
    } else {
        header("Location: contact.html?status=error&message=There was an error sending your message. Please try again later.");
    }
    exit;
} else {
    // Not a POST request, redirect to contact page
    header("Location: contact.html");
    exit;
}
?>
